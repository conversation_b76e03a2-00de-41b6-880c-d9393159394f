'use client';

import React, { ReactNode, useEffect, useState } from 'react';
import { useUser } from "@/hooks/use-user";
import { UserRole } from '@prisma/client';
import { hasPermission, hasPermissionSync } from '@/lib/authorization';

export const dynamic = 'force-dynamic';
interface RoleGuardProps {
  children: ReactNode;
  allowedRoles?: readonly UserRole[];
  requiredRole?: UserRole;
  resource?: string;
  action?: string;
  resourceData?: any;
  resourceId?: string;
  fallback?: ReactNode;
  showLoading?: boolean;
  loadingComponent?: ReactNode;
}

/**
 * RoleGuard component that conditionally renders children based on user permissions
 * 
 * @param children - Content to render if user has permission
 * @param allowedRoles - Array of roles that are allowed to see the content
 * @param requiredRole - Single role required (alternative to allowedRoles)
 * @param resource - Resource type for permission checking (e.g., 'JOB', 'SHIFT')
 * @param action - Action type for permission checking (e.g., 'READ', 'UPDATE')
 * @param resourceData - Resource data for context-aware permission checking
 * @param resourceId - Resource ID for permission checking
 * @param fallback - Component to render if user doesn't have permission
 * @param showLoading - Whether to show loading state
 * @param loadingComponent - Custom loading component
 */
export function RoleGuard({
  children,
  allowedRoles,
  requiredRole,
  resource,
  action,
  resourceData,
  resourceId,
  fallback = null,
  showLoading = true,
  loadingComponent,
}: RoleGuardProps) {
  const { user, isLoading } = useUser();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [permissionLoading, setPermissionLoading] = useState(false);

  // Check permissions when user or permission parameters change
  useEffect(() => {
    if (!user) {
      setHasAccess(false);
      return;
    }

    const checkPermissions = async () => {
      let access = false;

      // Check role-based access
      if (allowedRoles) {
        access = allowedRoles.includes(user.role as UserRole);
      } else if (requiredRole) {
        access = user.role === requiredRole;
      } else if (resource && action) {
        // Use permission-based access control
        const authenticatedUser = {
          ...user,
          avatarUrl: user.avatarUrl || '',
          company: user.company || null,
        };

        setPermissionLoading(true);
        try {
          access = await hasPermission(authenticatedUser, resource, action, {
            resource: resourceData,
            resourceId,
          });
        } catch (error) {
          console.error('Permission check failed:', error);
          access = false;
        } finally {
          setPermissionLoading(false);
        }
      } else {
        // Default to allowing access if no restrictions specified
        access = true;
      }

      setHasAccess(access);
    };

    checkPermissions();
  }, [user, allowedRoles, requiredRole, resource, action, resourceData, resourceId]);

  // Show loading state
  if ((isLoading || permissionLoading || hasAccess === null) && showLoading) {
    return loadingComponent || <div className="animate-pulse">Loading...</div>;
  }

  // No user - don't render anything
  if (!user) {
    return <>{fallback}</>;
  }

  return hasAccess ? <>{children}</> : <>{fallback}</>;
}

/**
 * Higher-order component version of RoleGuard
 */
export function withRoleGuard<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  guardProps: Omit<RoleGuardProps, 'children'>
) {
  return function GuardedComponent(props: P) {
    return (
      <RoleGuard {...guardProps}>
        <WrappedComponent {...props} />
      </RoleGuard>
    );
  };
}

/**
 * Hook to check if current user has specific permissions
 */
export function usePermissions() {
  const { user } = useUser();

  const checkRole = (allowedRoles: UserRole[]): boolean => {
    if (!user) return false;
    return allowedRoles.includes(user.role as UserRole);
  };

  const checkPermission = (
    resource: string,
    action: string,
    context: { resource?: any; resourceId?: string } = {}
  ): boolean => {
    if (!user) return false;

    const authenticatedUser = {
      ...user,
      avatarUrl: user.avatarUrl || '',
      company: user.company || null,
    };

    try {
      return hasPermissionSync(authenticatedUser, resource, action, context);
    } catch (error) {
      console.warn(`Permission check for ${resource}.${action} requires async handling`);
      return false;
    }
  };

  const isAdmin = (): boolean => {
    return user?.role === UserRole.Admin;
  };

  const isCrewChief = (): boolean => {
    return user?.role === UserRole.CrewChief;
  };

  const isCompanyUser = (): boolean => {
    return user?.role === UserRole.CompanyUser;
  };

  const isStagehand = (): boolean => {
    return user?.role === UserRole.StageHand;
  };

  const isManager = (): boolean => {
    return user?.role === UserRole.Manager;
  };

  const canAccess = (resource: string, action: string, context?: any): boolean => {
    return checkPermission(resource, action, context);
  };

  return {
    user,
    checkRole,
    checkPermission,
    isAdmin,
    isCrewChief,
    isCompanyUser,
    isStagehand,
    isManager,
    canAccess,
  };
}

/**
 * Predefined role combinations for common use cases
 */
export const ROLE_GROUPS = {
  ADMIN_ONLY: [UserRole.Admin],
  MANAGEMENT: [UserRole.Admin, UserRole.Manager],
  CREW_LEADERS: [UserRole.Admin, UserRole.Manager, UserRole.CrewChief],
  ALL_STAFF: [UserRole.Admin, UserRole.Manager, UserRole.CrewChief, UserRole.StageHand],
  COMPANY_ACCESS: [UserRole.Admin, UserRole.Manager, UserRole.CompanyUser],
  FIELD_WORKERS: [UserRole.CrewChief, UserRole.StageHand],
} as const;

/**
 * Component-specific guards for common UI patterns
 */
export function AdminOnlyGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={ROLE_GROUPS.ADMIN_ONLY} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function ManagementGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={ROLE_GROUPS.MANAGEMENT} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function CrewLeaderGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={ROLE_GROUPS.CREW_LEADERS} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

export function ClientAccessGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={ROLE_GROUPS.COMPANY_ACCESS} fallback={fallback}>
      {children}
    </RoleGuard>
  );
}

/**
 * Resource-specific guards
 */
export function JobGuard({ 
  children, 
  action, 
  jobData, 
  jobId, 
  fallback 
}: { 
  children: ReactNode; 
  action: string; 
  jobData?: any; 
  jobId?: string; 
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      resource="JOB"
      action={action}
      resourceData={jobData}
      resourceId={jobId}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

export function ShiftGuard({ 
  children, 
  action, 
  shiftData, 
  shiftId, 
  fallback 
}: { 
  children: ReactNode; 
  action: string; 
  shiftData?: any; 
  shiftId?: string; 
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      resource="SHIFT"
      action={action}
      resourceData={shiftData}
      resourceId={shiftId}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}

export function TimesheetGuard({ 
  children, 
  action, 
  timesheetData, 
  timesheetId, 
  fallback 
}: { 
  children: ReactNode; 
  action: string; 
  timesheetData?: any; 
  timesheetId?: string; 
  fallback?: ReactNode;
}) {
  return (
    <RoleGuard
      resource="TIMESHEET"
      action={action}
      resourceData={timesheetData}
      resourceId={timesheetId}
      fallback={fallback}
    >
      {children}
    </RoleGuard>
  );
}
